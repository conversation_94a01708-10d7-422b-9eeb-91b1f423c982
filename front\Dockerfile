FROM node:22-alpine AS base

RUN corepack enable pnpm
# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package*.json ./
COPY pnpm-lock.yaml* ./
RUN pnpm i --no-frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Accept build arguments for environment variables
ARG NEXT_PUBLIC_API_BASE_URL=http://localhost:16001
ARG NEXT_PUBLIC_SEARCH_API_URL=http://localhost:16001/api/search
ARG NEXT_PUBLIC_EADUAN_URL=https://eaduan.islam.gov.my
ARG NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL=https://myehalal.halal.gov.my/domestik/v1/
ARG NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL=https://myehalal.halal.gov.my/international/v1/

# Set environment variables for the build
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_SEARCH_API_URL=$NEXT_PUBLIC_SEARCH_API_URL
ENV NEXT_PUBLIC_EADUAN_URL=$NEXT_PUBLIC_EADUAN_URL
ENV NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL=$NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL
ENV NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL=$NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED=1

# Use existing next.config.ts with next-intl support
# No need to override the config file as it already has the correct configuration

RUN corepack enable pnpm
RUN pnpm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./
COPY --from=builder /app/node_modules ./node_modules

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next

USER nextjs

EXPOSE 16000

ENV PORT=16000
ENV HOSTNAME="0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:16000', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

CMD ["npm", "run", "start:standalone"]
