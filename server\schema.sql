-- WhatsApp configuration table
CREATE TABLE IF NOT EXISTS whatsapp_config (
  id SERIAL PRIMARY KEY,
  access_token TEXT NOT NULL,
  phone_number_id TEXT NOT NULL,
  webhook_verify_token TEXT NOT NULL,
  business_account_id TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
  id SERIAL PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP
);

-- WhatsApp messages table for logging
CREATE TABLE IF NOT EXISTS whatsapp_messages (
  id TEXT PRIMARY KEY,
  from_number TEXT NOT NULL,
  to_number TEXT NOT NULL,
  type TEXT NOT NULL,
  content TEXT NOT NULL,
  media_url TEXT,
  session_id TEXT,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  direction TEXT NOT NULL -- 'inbound' or 'outbound'
);

-- Facebook configuration table
CREATE TABLE IF NOT EXISTS facebook_config (
  id SERIAL PRIMARY KEY,
  page_access_token TEXT NOT NULL,
  page_id TEXT NOT NULL,
  app_secret TEXT NOT NULL,
  verify_token TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Facebook messages table for logging
CREATE TABLE IF NOT EXISTS facebook_messages (
  id TEXT PRIMARY KEY,
  from_user_id TEXT NOT NULL,
  to_page_id TEXT NOT NULL,
  type TEXT NOT NULL,
  content TEXT NOT NULL,
  media_url TEXT,
  session_id TEXT,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  direction TEXT NOT NULL -- 'inbound' or 'outbound'
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_from_number ON whatsapp_messages(from_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_timestamp ON whatsapp_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_session_id ON whatsapp_messages(session_id);

CREATE INDEX IF NOT EXISTS idx_facebook_messages_from_user_id ON facebook_messages(from_user_id);
CREATE INDEX IF NOT EXISTS idx_facebook_messages_timestamp ON facebook_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_facebook_messages_session_id ON facebook_messages(session_id);

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, password_hash)
VALUES ('admin', '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqKqKqKqKqKqKq')
ON CONFLICT (username) DO NOTHING;
