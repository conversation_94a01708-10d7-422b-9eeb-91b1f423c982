{"devDependencies": {"@biomejs/biome": "2.0.5", "@types/node": "^20.0.0", "concurrently": "^9.1.2", "tsx": "^4.0.0", "typescript": "^5.0.0", "wrangler": "^4.20.3"}, "scripts": {"dev": "concurrently \"cd server && bun dev\" \"cd front && bun dev\" \"cd admin && bun dev\"", "lint": "biome check .", "lint:fix": "biome check . --write", "format": "biome format . --write", "db:seed": "cd server && pnpm db:seed", "db:push": "cd server && pnpm db:push", "db:migrate": "cd server && pnpm db:migrate && pnpm db:seed", "db:studio": "cd server && pnpm db:studio", "db:gen": "cd server && pnpm drizzle:generate", "deploy:server": "cd server && pnpm db:migrate:prod && wrangler deploy -e production", "deploy:front": "cd front && wrangler pages deploy --branch production", "deploy": "bun deploy:server && bun deploy:front", "server:logs": "cd server && wrangler -e production tail -f", "test": "bun test --bail --serial ."}, "dependencies": {"axios": "^1.10.0", "class-variance-authority": "^0.7.1"}}