import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { LoginRequest, LoginResponse, User } from '@/types';

interface AuthState {
  // State
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  // Actions
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  refreshUser: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest): Promise<boolean> => {
        set({ isLoading: true, error: null });

        try {
          console.log('Admin login attempt with API_BASE_URL:', API_BASE_URL);
          const response = await fetch(`${API_BASE_URL}/api/admin/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          });

          const data: LoginResponse = await response.json();
          console.log('Login response:', data);

          if (data.success && data.token && data.user) {
            // Check if user has SUPERADMIN role
            console.log('User roles:', data.user.roles);
            if (!data.user.roles.includes('SUPERADMIN' as any)) {
              console.log('Access denied: SUPERADMIN role not found');
              set({
                error: 'Access denied. SUPERADMIN role required.',
                isLoading: false,
              });
              return false;
            }

            console.log('Login successful, setting auth state');
            set({
              user: data.user,
              token: data.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return true;
          }
          console.log('Login failed:', data.error);
          set({
            error: data.error || 'Login failed',
            isLoading: false,
          });
          return false;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Network error',
            isLoading: false,
          });
          return false;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      refreshUser: async () => {
        const { token } = get();
        if (!token) return;

        try {
          const response = await fetch(`${API_BASE_URL}/api/admin/profile`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const userData = await response.json();
            set({ user: userData });
          } else {
            // Token might be invalid, logout
            get().logout();
          }
        } catch (error) {
          console.error('Failed to refresh user:', error);
        }
      },
    }),
    {
      name: 'halal-admin-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    },
  ),
);
